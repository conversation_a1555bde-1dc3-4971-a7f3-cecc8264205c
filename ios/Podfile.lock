PODS:
  - abseil/algorithm (1.20211102.0):
    - abseil/algorithm/algorithm (= 1.20211102.0)
    - abseil/algorithm/container (= 1.20211102.0)
  - abseil/algorithm/algorithm (1.20211102.0):
    - abseil/base/config
  - abseil/algorithm/container (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/base (1.20211102.0):
    - abseil/base/atomic_hook (= 1.20211102.0)
    - abseil/base/base (= 1.20211102.0)
    - abseil/base/base_internal (= 1.20211102.0)
    - abseil/base/config (= 1.20211102.0)
    - abseil/base/core_headers (= 1.20211102.0)
    - abseil/base/dynamic_annotations (= 1.20211102.0)
    - abseil/base/endian (= 1.20211102.0)
    - abseil/base/errno_saver (= 1.20211102.0)
    - abseil/base/fast_type_id (= 1.20211102.0)
    - abseil/base/log_severity (= 1.20211102.0)
    - abseil/base/malloc_internal (= 1.20211102.0)
    - abseil/base/pretty_function (= 1.20211102.0)
    - abseil/base/raw_logging_internal (= 1.20211102.0)
    - abseil/base/spinlock_wait (= 1.20211102.0)
    - abseil/base/strerror (= 1.20211102.0)
    - abseil/base/throw_delegate (= 1.20211102.0)
  - abseil/base/atomic_hook (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/base (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
  - abseil/base/base_internal (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
  - abseil/base/config (1.20211102.0)
  - abseil/base/core_headers (1.20211102.0):
    - abseil/base/config
  - abseil/base/dynamic_annotations (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/endian (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/errno_saver (1.20211102.0):
    - abseil/base/config
  - abseil/base/fast_type_id (1.20211102.0):
    - abseil/base/config
  - abseil/base/log_severity (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/malloc_internal (1.20211102.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
  - abseil/base/pretty_function (1.20211102.0)
  - abseil/base/raw_logging_internal (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
  - abseil/base/spinlock_wait (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/strerror (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/throw_delegate (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/container/common (1.20211102.0):
    - abseil/meta/type_traits
    - abseil/types/optional
  - abseil/container/compressed_tuple (1.20211102.0):
    - abseil/utility/utility
  - abseil/container/container_memory (1.20211102.0):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/container/fixed_array (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
  - abseil/container/flat_hash_map (1.20211102.0):
    - abseil/algorithm/container
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
  - abseil/container/hash_function_defaults (1.20211102.0):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/cord
    - abseil/strings/strings
  - abseil/container/hash_policy_traits (1.20211102.0):
    - abseil/meta/type_traits
  - abseil/container/hashtable_debug_hooks (1.20211102.0):
    - abseil/base/config
  - abseil/container/hashtablez_sampler (1.20211102.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/container/have_sse
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/utility/utility
  - abseil/container/have_sse (1.20211102.0)
  - abseil/container/inlined_vector (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
  - abseil/container/inlined_vector_internal (1.20211102.0):
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
  - abseil/container/layout (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
  - abseil/container/raw_hash_map (1.20211102.0):
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
  - abseil/container/raw_hash_set (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/container/have_sse
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
  - abseil/debugging/debugging_internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
  - abseil/debugging/demangle_internal (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/debugging/stacktrace (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/debugging_internal
  - abseil/debugging/symbolize (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
  - abseil/functional/bind_front (1.20211102.0):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/functional/function_ref (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/hash/city (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
  - abseil/hash/hash (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/hash/low_level_hash (1.20211102.0):
    - abseil/base/config
    - abseil/base/endian
    - abseil/numeric/bits
    - abseil/numeric/int128
  - abseil/memory (1.20211102.0):
    - abseil/memory/memory (= 1.20211102.0)
  - abseil/memory/memory (1.20211102.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/meta (1.20211102.0):
    - abseil/meta/type_traits (= 1.20211102.0)
  - abseil/meta/type_traits (1.20211102.0):
    - abseil/base/config
  - abseil/numeric/bits (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/numeric/int128 (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
  - abseil/numeric/representation (1.20211102.0):
    - abseil/base/config
  - abseil/profiling/exponential_biased (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/profiling/sample_recorder (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
  - abseil/random/distributions (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
  - abseil/random/internal/distribution_caller (1.20211102.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
  - abseil/random/internal/fast_uniform_bits (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
  - abseil/random/internal/fastmath (1.20211102.0):
    - abseil/numeric/bits
  - abseil/random/internal/generate_real (1.20211102.0):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
  - abseil/random/internal/iostream_state_saver (1.20211102.0):
    - abseil/meta/type_traits
    - abseil/numeric/int128
  - abseil/random/internal/nonsecure_base (1.20211102.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/pcg_engine (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
  - abseil/random/internal/platform (1.20211102.0):
    - abseil/base/config
  - abseil/random/internal/pool_urbg (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/random/internal/randen (1.20211102.0):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
  - abseil/random/internal/randen_engine (1.20211102.0):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
  - abseil/random/internal/randen_hwaes (1.20211102.0):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
  - abseil/random/internal/randen_hwaes_impl (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/randen_slow (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/salted_seed_seq (1.20211102.0):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/seed_material (1.20211102.0):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/traits (1.20211102.0):
    - abseil/base/config
  - abseil/random/internal/uniform_helper (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
  - abseil/random/internal/wide_multiply (1.20211102.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
  - abseil/random/random (1.20211102.0):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
  - abseil/random/seed_gen_exception (1.20211102.0):
    - abseil/base/config
  - abseil/random/seed_sequences (1.20211102.0):
    - abseil/container/inlined_vector
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/status/status (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/status/statusor (1.20211102.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/strings/cord (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/strings/cord_internal (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
  - abseil/strings/cordz_functions (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
  - abseil/strings/cordz_handle (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
  - abseil/strings/cordz_info (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/types/span
  - abseil/strings/cordz_statistics (1.20211102.0):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_scope (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_tracker (1.20211102.0):
    - abseil/base/config
  - abseil/strings/internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
  - abseil/strings/str_format (1.20211102.0):
    - abseil/strings/str_format_internal
  - abseil/strings/str_format_internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/strings/strings (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/internal
  - abseil/synchronization/graphcycles_internal (1.20211102.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
  - abseil/synchronization/kernel_timeout_internal (1.20211102.0):
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
  - abseil/synchronization/synchronization (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
  - abseil/time (1.20211102.0):
    - abseil/time/internal (= 1.20211102.0)
    - abseil/time/time (= 1.20211102.0)
  - abseil/time/internal (1.20211102.0):
    - abseil/time/internal/cctz (= 1.20211102.0)
  - abseil/time/internal/cctz (1.20211102.0):
    - abseil/time/internal/cctz/civil_time (= 1.20211102.0)
    - abseil/time/internal/cctz/time_zone (= 1.20211102.0)
  - abseil/time/internal/cctz/civil_time (1.20211102.0):
    - abseil/base/config
  - abseil/time/internal/cctz/time_zone (1.20211102.0):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
  - abseil/time/time (1.20211102.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
  - abseil/types (1.20211102.0):
    - abseil/types/any (= 1.20211102.0)
    - abseil/types/bad_any_cast (= 1.20211102.0)
    - abseil/types/bad_any_cast_impl (= 1.20211102.0)
    - abseil/types/bad_optional_access (= 1.20211102.0)
    - abseil/types/bad_variant_access (= 1.20211102.0)
    - abseil/types/compare (= 1.20211102.0)
    - abseil/types/optional (= 1.20211102.0)
    - abseil/types/span (= 1.20211102.0)
    - abseil/types/variant (= 1.20211102.0)
  - abseil/types/any (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
  - abseil/types/bad_any_cast (1.20211102.0):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
  - abseil/types/bad_any_cast_impl (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_optional_access (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_variant_access (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/compare (1.20211102.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/types/optional (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
  - abseil/types/span (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
  - abseil/types/variant (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
  - abseil/utility/utility (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - BEMCheckBox (1.4.1)
  - boost (1.76.0)
  - BoringSSL-GRPC (0.0.24):
    - BoringSSL-GRPC/Implementation (= 0.0.24)
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Implementation (0.0.24):
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Interface (0.0.24)
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - FBAEMKit (13.2.0):
    - FBSDKCoreKit_Basics (= 13.2.0)
  - FBLazyVector (0.72.3)
  - FBReactNativeSpec (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.72.3)
    - RCTTypeSafety (= 0.72.3)
    - React-Core (= 0.72.3)
    - React-jsi (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - FBSDKCoreKit (13.2.0):
    - FBAEMKit (= 13.2.0)
    - FBSDKCoreKit_Basics (= 13.2.0)
  - FBSDKCoreKit_Basics (13.2.0)
  - FBSDKLoginKit (13.2.0):
    - FBSDKCoreKit (= 13.2.0)
  - Firebase/AnalyticsWithoutAdIdSupport (8.15.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics/WithoutAdIdSupport (~> 8.15.0)
  - Firebase/Auth (8.15.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 8.15.0)
  - Firebase/CoreOnly (8.15.0):
    - FirebaseCore (= 8.15.0)
  - Firebase/Crashlytics (8.15.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 8.15.0)
  - Firebase/DynamicLinks (8.15.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 8.15.0)
  - Firebase/Firestore (8.15.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 8.15.0)
  - Firebase/Messaging (8.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 8.15.0)
  - Firebase/Performance (8.15.0):
    - Firebase/CoreOnly
    - FirebasePerformance (~> 8.15.0)
  - Firebase/Storage (8.15.0):
    - Firebase/CoreOnly
    - FirebaseStorage (~> 8.15.0)
  - FirebaseABTesting (8.15.0):
    - FirebaseCore (~> 8.0)
  - FirebaseAnalytics/WithoutAdIdSupport (8.15.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleAppMeasurement/WithoutAdIdSupport (= 8.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - FirebaseAuth (8.15.0):
    - FirebaseCore (~> 8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/Environment (~> 7.7)
    - GTMSessionFetcher/Core (~> 1.5)
  - FirebaseCore (8.15.0):
    - FirebaseCoreDiagnostics (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
  - FirebaseCoreDiagnostics (8.15.0):
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (~> 2.30908.0)
  - FirebaseCrashlytics (8.15.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (~> 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - FirebaseDynamicLinks (8.15.0):
    - FirebaseCore (~> 8.0)
  - FirebaseFirestore (8.15.0):
    - abseil/algorithm (~> 1.20211102.0)
    - abseil/base (~> 1.20211102.0)
    - abseil/container/flat_hash_map (~> 1.20211102.0)
    - abseil/memory (~> 1.20211102.0)
    - abseil/meta (~> 1.20211102.0)
    - abseil/strings/strings (~> 1.20211102.0)
    - abseil/time (~> 1.20211102.0)
    - abseil/types (~> 1.20211102.0)
    - FirebaseCore (~> 8.0)
    - "gRPC-C++ (~> 1.44.0)"
    - leveldb-library (~> 1.22)
    - nanopb (~> 2.30908.0)
  - FirebaseInstallations (8.15.0):
    - FirebaseCore (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - PromisesObjC (< 3.0, >= 1.2)
  - FirebaseMessaging (8.15.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Reachability (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - nanopb (~> 2.30908.0)
  - FirebasePerformance (8.15.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - FirebaseRemoteConfig (~> 8.0)
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/ISASwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - nanopb (~> 2.30908.0)
  - FirebaseRemoteConfig (8.15.0):
    - FirebaseABTesting (~> 8.0)
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
  - FirebaseStorage (8.15.0):
    - FirebaseCore (~> 8.0)
    - GTMSessionFetcher/Core (~> 1.5)
  - fmt (6.2.1)
  - glog (0.3.5)
  - Google-Maps-iOS-Utils (4.2.2):
    - Google-Maps-iOS-Utils/Clustering (= 4.2.2)
    - Google-Maps-iOS-Utils/Geometry (= 4.2.2)
    - Google-Maps-iOS-Utils/GeometryUtils (= 4.2.2)
    - Google-Maps-iOS-Utils/Heatmap (= 4.2.2)
    - Google-Maps-iOS-Utils/QuadTree (= 4.2.2)
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Clustering (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Geometry (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/GeometryUtils (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Heatmap (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/QuadTree (4.2.2):
    - GoogleMaps (~> 7.3)
  - GoogleAppMeasurement/WithoutAdIdSupport (8.15.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - GoogleSignIn (7.0.0):
    - AppAuth (~> 1.5)
    - GTMAppAuth (< 3.0, >= 1.3)
    - GTMSessionFetcher/Core (< 4.0, >= 1.1)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.44.0)":
    - "gRPC-C++/Implementation (= 1.44.0)"
    - "gRPC-C++/Interface (= 1.44.0)"
  - "gRPC-C++/Implementation (1.44.0)":
    - abseil/base/base (= 1.20211102.0)
    - abseil/base/core_headers (= 1.20211102.0)
    - abseil/container/flat_hash_map (= 1.20211102.0)
    - abseil/container/inlined_vector (= 1.20211102.0)
    - abseil/functional/bind_front (= 1.20211102.0)
    - abseil/hash/hash (= 1.20211102.0)
    - abseil/memory/memory (= 1.20211102.0)
    - abseil/random/random (= 1.20211102.0)
    - abseil/status/status (= 1.20211102.0)
    - abseil/status/statusor (= 1.20211102.0)
    - abseil/strings/cord (= 1.20211102.0)
    - abseil/strings/str_format (= 1.20211102.0)
    - abseil/strings/strings (= 1.20211102.0)
    - abseil/synchronization/synchronization (= 1.20211102.0)
    - abseil/time/time (= 1.20211102.0)
    - abseil/types/optional (= 1.20211102.0)
    - abseil/types/variant (= 1.20211102.0)
    - abseil/utility/utility (= 1.20211102.0)
    - "gRPC-C++/Interface (= 1.44.0)"
    - gRPC-Core (= 1.44.0)
  - "gRPC-C++/Interface (1.44.0)"
  - gRPC-Core (1.44.0):
    - gRPC-Core/Implementation (= 1.44.0)
    - gRPC-Core/Interface (= 1.44.0)
  - gRPC-Core/Implementation (1.44.0):
    - abseil/base/base (= 1.20211102.0)
    - abseil/base/core_headers (= 1.20211102.0)
    - abseil/container/flat_hash_map (= 1.20211102.0)
    - abseil/container/inlined_vector (= 1.20211102.0)
    - abseil/functional/bind_front (= 1.20211102.0)
    - abseil/hash/hash (= 1.20211102.0)
    - abseil/memory/memory (= 1.20211102.0)
    - abseil/random/random (= 1.20211102.0)
    - abseil/status/status (= 1.20211102.0)
    - abseil/status/statusor (= 1.20211102.0)
    - abseil/strings/cord (= 1.20211102.0)
    - abseil/strings/str_format (= 1.20211102.0)
    - abseil/strings/strings (= 1.20211102.0)
    - abseil/synchronization/synchronization (= 1.20211102.0)
    - abseil/time/time (= 1.20211102.0)
    - abseil/types/optional (= 1.20211102.0)
    - abseil/types/variant (= 1.20211102.0)
    - abseil/utility/utility (= 1.20211102.0)
    - BoringSSL-GRPC (= 0.0.24)
    - gRPC-Core/Interface (= 1.44.0)
    - Libuv-gRPC (= 0.0.10)
  - gRPC-Core/Interface (1.44.0)
  - GTMAppAuth (2.0.0):
    - AppAuth/Core (~> 1.6)
    - GTMSessionFetcher/Core (< 4.0, >= 1.5)
  - GTMSessionFetcher/Core (1.7.2)
  - hermes-engine (0.72.3):
    - hermes-engine/Pre-built (= 0.72.3)
  - hermes-engine/Pre-built (0.72.3)
  - leveldb-library (1.22.6)
  - libevent (2.1.12)
  - Libuv-gRPC (0.0.10):
    - Libuv-gRPC/Implementation (= 0.0.10)
    - Libuv-gRPC/Interface (= 0.0.10)
  - Libuv-gRPC/Implementation (0.0.10):
    - Libuv-gRPC/Interface (= 0.0.10)
  - Libuv-gRPC/Interface (0.0.10)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - lottie-ios (4.5.0)
  - lottie-react-native (7.2.2):
    - lottie-ios (= 4.5.0)
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - OneSignalXCFramework (3.12.10):
    - OneSignalXCFramework/OneSignalCore (= 3.12.10)
    - OneSignalXCFramework/OneSignalExtension (= 3.12.10)
    - OneSignalXCFramework/OneSignalOutcomes (= 3.12.10)
  - OneSignalXCFramework/OneSignalCore (3.12.10)
  - OneSignalXCFramework/OneSignalExtension (3.12.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOutcomes (3.12.10):
    - OneSignalXCFramework/OneSignalCore
  - PromisesObjC (2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.72.3)
  - RCTTypeSafety (0.72.3):
    - FBLazyVector (= 0.72.3)
    - RCTRequired (= 0.72.3)
    - React-Core (= 0.72.3)
  - React (0.72.3):
    - React-Core (= 0.72.3)
    - React-Core/DevSupport (= 0.72.3)
    - React-Core/RCTWebSocket (= 0.72.3)
    - React-RCTActionSheet (= 0.72.3)
    - React-RCTAnimation (= 0.72.3)
    - React-RCTBlob (= 0.72.3)
    - React-RCTImage (= 0.72.3)
    - React-RCTLinking (= 0.72.3)
    - React-RCTNetwork (= 0.72.3)
    - React-RCTSettings (= 0.72.3)
    - React-RCTText (= 0.72.3)
    - React-RCTVibration (= 0.72.3)
  - React-callinvoker (0.72.3)
  - React-Codegen (0.72.3):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.3)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.3)
    - React-Core/RCTWebSocket (= 0.72.3)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.3)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.3)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.3)
    - React-Codegen (= 0.72.3)
    - React-Core/CoreModulesHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-RCTBlob
    - React-RCTImage (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.3):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.3)
    - React-debug (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-jsinspector (= 0.72.3)
    - React-logger (= 0.72.3)
    - React-perflogger (= 0.72.3)
    - React-runtimeexecutor (= 0.72.3)
  - React-debug (0.72.3)
  - React-hermes (0.72.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.72.3)
    - React-jsi
    - React-jsiexecutor (= 0.72.3)
    - React-jsinspector (= 0.72.3)
    - React-perflogger (= 0.72.3)
  - React-jsi (0.72.3):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-perflogger (= 0.72.3)
  - React-jsinspector (0.72.3)
  - React-logger (0.72.3):
    - glog
  - react-native-add-calendar-event (5.0.0):
    - React-Core
  - react-native-blur (4.4.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-config (1.5.5):
    - react-native-config/App (= 1.5.5)
  - react-native-config/App (1.5.5):
    - React-Core
  - react-native-context-menu-view (1.19.0):
    - React
  - react-native-date-picker (4.4.2):
    - React-Core
  - react-native-device-country (1.1.1):
    - React-Core
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-get-random-values (1.11.0):
    - React-Core
  - react-native-google-maps (1.11.3):
    - Google-Maps-iOS-Utils (= 4.2.2)
    - GoogleMaps (= 7.4.0)
    - React-Core
  - react-native-keyboard-controller (1.12.7):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-maps (1.11.3):
    - React-Core
  - react-native-netinfo (9.5.0):
    - React-Core
  - react-native-onesignal (4.5.4):
    - OneSignalXCFramework (= 3.12.10)
    - React (< 1.0.0, >= 0.13.0)
  - react-native-pager-view (6.8.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-safe-area-context (4.14.1):
    - React-Core
  - react-native-slider (4.5.7):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-version-check (3.5.0):
    - React-Core
  - react-native-wallet (1.0.8):
    - React
  - react-native-webview (13.14.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - React-NativeModulesApple (0.72.3):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.3)
  - React-RCTActionSheet (0.72.3):
    - React-Core/RCTActionSheetHeaders (= 0.72.3)
  - React-RCTAnimation (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.3)
    - React-Codegen (= 0.72.3)
    - React-Core/RCTAnimationHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-RCTAppDelegate (0.72.3):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.3):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.3)
    - React-Core/RCTBlobHeaders (= 0.72.3)
    - React-Core/RCTWebSocket (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-RCTNetwork (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-RCTImage (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.3)
    - React-Codegen (= 0.72.3)
    - React-Core/RCTImageHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-RCTNetwork (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-RCTLinking (0.72.3):
    - React-Codegen (= 0.72.3)
    - React-Core/RCTLinkingHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-RCTNetwork (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.3)
    - React-Codegen (= 0.72.3)
    - React-Core/RCTNetworkHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-RCTSettings (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.3)
    - React-Codegen (= 0.72.3)
    - React-Core/RCTSettingsHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-RCTText (0.72.3):
    - React-Core/RCTTextHeaders (= 0.72.3)
  - React-RCTVibration (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.3)
    - React-Core/RCTVibrationHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-rncore (0.72.3)
  - React-runtimeexecutor (0.72.3):
    - React-jsi (= 0.72.3)
  - React-runtimescheduler (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.3)
    - React-cxxreact (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-logger (= 0.72.3)
    - React-perflogger (= 0.72.3)
  - ReactCommon/turbomodule/core (0.72.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.3)
    - React-cxxreact (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-logger (= 0.72.3)
    - React-perflogger (= 0.72.3)
  - RNAppleAuthentication (2.4.1):
    - React-Core
  - RNCalendarEvents (2.2.0):
    - React
  - RNCAsyncStorage (1.24.0):
    - React-Core
  - RNCCheckbox (0.5.20):
    - BEMCheckBox (~> 1.4)
    - React-Core
  - RNCMaskedView (0.2.9):
    - React-Core
  - RNDeviceInfo (10.14.0):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBAnalytics (14.12.0):
    - Firebase/AnalyticsWithoutAdIdSupport (= 8.15.0)
    - React-Core
    - RNFBApp
  - RNFBApp (14.12.0):
    - Firebase/CoreOnly (= 8.15.0)
    - React-Core
  - RNFBAuth (14.12.0):
    - Firebase/Auth (= 8.15.0)
    - React-Core
    - RNFBApp
  - RNFBCrashlytics (14.12.0):
    - Firebase/Crashlytics (= 8.15.0)
    - React-Core
    - RNFBApp
  - RNFBDynamicLinks (14.12.0):
    - Firebase/DynamicLinks (= 8.15.0)
    - GoogleUtilities/AppDelegateSwizzler
    - React-Core
    - RNFBApp
  - RNFBFirestore (14.12.0):
    - Firebase/Firestore (= 8.15.0)
    - React-Core
    - RNFBApp
  - RNFBMessaging (14.12.0):
    - Firebase/Messaging (= 8.15.0)
    - React-Core
    - RNFBApp
  - RNFBPerf (14.12.0):
    - Firebase/Performance (= 8.15.0)
    - React-Core
    - RNFBApp
  - RNFBStorage (14.12.0):
    - Firebase/Storage (= 8.15.0)
    - React-Core
    - RNFBApp
  - RNFlashList (1.8.2):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNGestureHandler (2.12.1):
    - React-Core
  - RNGoogleSignin (10.1.2):
    - GoogleSignIn (~> 7.0.0)
    - React-Core
  - RNImageCropPicker (0.41.2):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.41.2)
    - TOCropViewController (~> 2.7.4)
  - RNImageCropPicker/QBImagePickerController (0.41.2):
    - React-Core
    - React-RCTImage
    - TOCropViewController (~> 2.7.4)
  - RNNotifee (7.9.0):
    - React-Core
    - RNNotifee/NotifeeCore (= 7.9.0)
  - RNNotifee/NotifeeCore (7.9.0):
    - React-Core
  - RNPermissions (4.1.5):
    - React-Core
  - RNReactNativeHapticFeedback (2.3.3):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNReanimated (3.4.2):
    - DoubleConversion
    - FBLazyVector
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTAppDelegate
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.10.2):
    - React-Core
    - React-RCTImage
  - RNShare (11.1.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNSVG (13.14.1):
    - React-Core
  - RNVectorIcons (10.2.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - SocketRocket (0.6.1)
  - Stripe (23.32.0):
    - StripeApplePay (= 23.32.0)
    - StripeCore (= 23.32.0)
    - StripePayments (= 23.32.0)
    - StripePaymentsUI (= 23.32.0)
    - StripeUICore (= 23.32.0)
  - stripe-react-native (0.40.0):
    - React-Core
    - Stripe (~> 23.32.0)
    - StripeApplePay (~> 23.32.0)
    - StripeFinancialConnections (~> 23.32.0)
    - StripePayments (~> 23.32.0)
    - StripePaymentSheet (~> 23.32.0)
    - StripePaymentsUI (~> 23.32.0)
  - StripeApplePay (23.32.0):
    - StripeCore (= 23.32.0)
  - StripeCore (23.32.0)
  - StripeFinancialConnections (23.32.0):
    - StripeCore (= 23.32.0)
    - StripeUICore (= 23.32.0)
  - StripePayments (23.32.0):
    - StripeCore (= 23.32.0)
    - StripePayments/Stripe3DS2 (= 23.32.0)
  - StripePayments/Stripe3DS2 (23.32.0):
    - StripeCore (= 23.32.0)
  - StripePaymentSheet (23.32.0):
    - StripeApplePay (= 23.32.0)
    - StripeCore (= 23.32.0)
    - StripePayments (= 23.32.0)
    - StripePaymentsUI (= 23.32.0)
  - StripePaymentsUI (23.32.0):
    - StripeCore (= 23.32.0)
    - StripePayments (= 23.32.0)
    - StripeUICore (= 23.32.0)
  - StripeUICore (23.32.0):
    - StripeCore (= 23.32.0)
  - TOCropViewController (2.7.4)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - FBSDKCoreKit (~> 13.0)
  - FBSDKLoginKit (~> 13.0)
  - FirebaseCore
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - Google-Maps-iOS-Utils (= 4.2.2)
  - GoogleMaps
  - GoogleUtilities
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - OneSignalXCFramework (< 4.0, >= 3.0.0)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-add-calendar-event (from `../node_modules/react-native-add-calendar-event`)
  - "react-native-blur (from `../node_modules/@react-native-community/blur`)"
  - react-native-camera (from `../node_modules/react-native-camera`)
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-context-menu-view (from `../node_modules/react-native-context-menu-view`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-device-country (from `../node_modules/react-native-device-country`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-get-random-values (from `../node_modules/react-native-get-random-values`)
  - react-native-google-maps (from `../node_modules/react-native-maps`)
  - react-native-keyboard-controller (from `../node_modules/react-native-keyboard-controller`)
  - react-native-maps (from `../node_modules/react-native-maps`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-onesignal (from `../node_modules/react-native-onesignal`)
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-version-check (from `../node_modules/react-native-version-check`)
  - react-native-wallet (from `../node_modules/react-native-wallet`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNAppleAuthentication (from `../node_modules/@invertase/react-native-apple-authentication`)"
  - RNCalendarEvents (from `../node_modules/react-native-calendar-events`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCCheckbox (from `../node_modules/@react-native-community/checkbox`)"
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBAuth (from `../node_modules/@react-native-firebase/auth`)"
  - "RNFBCrashlytics (from `../node_modules/@react-native-firebase/crashlytics`)"
  - "RNFBDynamicLinks (from `../node_modules/@react-native-firebase/dynamic-links`)"
  - "RNFBFirestore (from `../node_modules/@react-native-firebase/firestore`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - "RNFBPerf (from `../node_modules/@react-native-firebase/perf`)"
  - "RNFBStorage (from `../node_modules/@react-native-firebase/storage`)"
  - "RNFlashList (from `../node_modules/@shopify/flash-list`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - "RNGoogleSignin (from `../node_modules/@react-native-google-signin/google-signin`)"
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - "RNNotifee (from `../node_modules/@notifee/react-native`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReactNativeHapticFeedback (from `../node_modules/react-native-haptic-feedback`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - "stripe-react-native (from `../node_modules/@stripe/stripe-react-native`)"
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - abseil
    - AppAuth
    - BEMCheckBox
    - BoringSSL-GRPC
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseCrashlytics
    - FirebaseDynamicLinks
    - FirebaseFirestore
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebasePerformance
    - FirebaseRemoteConfig
    - FirebaseStorage
    - fmt
    - Google-Maps-iOS-Utils
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleSignIn
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMAppAuth
    - GTMSessionFetcher
    - leveldb-library
    - libevent
    - Libuv-gRPC
    - libwebp
    - lottie-ios
    - nanopb
    - OneSignalXCFramework
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore
    - TOCropViewController

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2023-03-20-RNv0.72.0-49794cfc7c81fb8f69fd60c3bbf85a7480cc5a77
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-add-calendar-event:
    :path: "../node_modules/react-native-add-calendar-event"
  react-native-blur:
    :path: "../node_modules/@react-native-community/blur"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-context-menu-view:
    :path: "../node_modules/react-native-context-menu-view"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-device-country:
    :path: "../node_modules/react-native-device-country"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-get-random-values:
    :path: "../node_modules/react-native-get-random-values"
  react-native-google-maps:
    :path: "../node_modules/react-native-maps"
  react-native-keyboard-controller:
    :path: "../node_modules/react-native-keyboard-controller"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-onesignal:
    :path: "../node_modules/react-native-onesignal"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-version-check:
    :path: "../node_modules/react-native-version-check"
  react-native-wallet:
    :path: "../node_modules/react-native-wallet"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNAppleAuthentication:
    :path: "../node_modules/@invertase/react-native-apple-authentication"
  RNCalendarEvents:
    :path: "../node_modules/react-native-calendar-events"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCCheckbox:
    :path: "../node_modules/@react-native-community/checkbox"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBAuth:
    :path: "../node_modules/@react-native-firebase/auth"
  RNFBCrashlytics:
    :path: "../node_modules/@react-native-firebase/crashlytics"
  RNFBDynamicLinks:
    :path: "../node_modules/@react-native-firebase/dynamic-links"
  RNFBFirestore:
    :path: "../node_modules/@react-native-firebase/firestore"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFBPerf:
    :path: "../node_modules/@react-native-firebase/perf"
  RNFBStorage:
    :path: "../node_modules/@react-native-firebase/storage"
  RNFlashList:
    :path: "../node_modules/@shopify/flash-list"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNGoogleSignin:
    :path: "../node_modules/@react-native-google-signin/google-signin"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNNotifee:
    :path: "../node_modules/@notifee/react-native"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReactNativeHapticFeedback:
    :path: "../node_modules/react-native-haptic-feedback"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  stripe-react-native:
    :path: "../node_modules/@stripe/stripe-react-native"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  abseil: ebe5b5529fb05d93a8bdb7951607be08b7fa71bc
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  BEMCheckBox: 5ba6e37ade3d3657b36caecc35c8b75c6c2b1a4e
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  BoringSSL-GRPC: 3175b25143e648463a56daeaaa499c6cb86dad33
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBAEMKit: c2b2895363b7e57192013d1dc49fdf498b28624c
  FBLazyVector: 4cce221dd782d3ff7c4172167bba09d58af67ccb
  FBReactNativeSpec: c6bd9e179757b3c0ecf815864fae8032377903ef
  FBSDKCoreKit: 58139803d861e72c7661dc875611a759352a55ac
  FBSDKCoreKit_Basics: 1ffc68326a5ece051d85574f02a0adcf27c2a5f2
  FBSDKLoginKit: 19e2a878556c2ee4f20486dc406e582783cd7578
  Firebase: 5f8193dff4b5b7c5d5ef72ae54bb76c08e2b841d
  FirebaseABTesting: 10cbce8db9985ae2e3847ea44e9947dd18f94e10
  FirebaseAnalytics: 7761cbadb00a717d8d0939363eb46041526474fa
  FirebaseAuth: 3e73bf8abf4fbb40f8b421f361f4cc48ee57388c
  FirebaseCore: 5743c5785c074a794d35f2fff7ecc254a91e08b1
  FirebaseCoreDiagnostics: 92e07a649aeb66352b319d43bdd2ee3942af84cb
  FirebaseCrashlytics: feb07e4e9187be3c23c6a846cce4824e5ce2dd0b
  FirebaseDynamicLinks: 1dc816ef789c5adac6fede0b46d11478175c70e4
  FirebaseFirestore: d7023faff8e1b4fd69d0adbcf18e65129bc03842
  FirebaseInstallations: 40bd9054049b2eae9a2c38ef1c3dd213df3605cd
  FirebaseMessaging: 5e5118a2383b3531e730d974680954c679ca0a13
  FirebasePerformance: 66eb58c3e3568a0501a9be271c8ff424dea0ff34
  FirebaseRemoteConfig: 2d6e2cfdb49af79535c8af8a80a4a5009038ec2b
  FirebaseStorage: 8019af461599b2c3bc61c6a5dbdfa3d2de66a4d9
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  Google-Maps-iOS-Utils: f77eab4c4326d7e6a277f8e23a0232402731913a
  GoogleAppMeasurement: 4c19f031220c72464d460c9daa1fb5d1acce958e
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  GoogleSignIn: b232380cf495a429b8095d3178a8d5855b42e842
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  "gRPC-C++": 9675f953ace2b3de7c506039d77be1f2e77a8db2
  gRPC-Core: 943e491cb0d45598b0b0eb9e910c88080369290b
  GTMAppAuth: 99fb010047ba3973b7026e45393f51f27ab965ae
  GTMSessionFetcher: 5595ec75acf5be50814f81e9189490412bad82ba
  hermes-engine: 10fbd3f62405c41ea07e71973ea61e1878d07322
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  Libuv-gRPC: 55e51798e14ef436ad9bc45d12d43b77b49df378
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  lottie-ios: a881093fab623c467d3bce374367755c272bdd59
  lottie-react-native: a0fb2925319e8af9dfea68a29ad2da3ba311b804
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  OneSignalXCFramework: e458f7a374192598a1d66418e1b61481c3cdf0e9
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 8dc08ca5a393b48b1c523ab6220dfdcc0fe000ad
  RCTRequired: a2faf4bad4e438ca37b2040cb8f7799baa065c18
  RCTTypeSafety: cb09f3e4747b6d18331a15eb05271de7441ca0b3
  React: 13109005b5353095c052f26af37413340ccf7a5d
  React-callinvoker: c8c87bce983aa499c13cb06d4447c025a35274d6
  React-Codegen: 56d9e5db91c1f2f10b106d8426dde6ff914bc88b
  React-Core: aaacca67f9ca54f142827e67f44bfbd815875eb6
  React-CoreModules: 32fab1d62416849a3b6dac6feff9d54e5ddc2d1e
  React-cxxreact: 37765b4975541105b2a3322a4b473417c158c869
  React-debug: 18722ab4399f77efc523660c7d1a7213e0e12a9c
  React-hermes: 935ae71fb3d7654e947beba8498835cd5e479707
  React-jsi: ec628dc7a15ffea969f237b0ea6d2fde212b19dd
  React-jsiexecutor: 59d1eb03af7d30b7d66589c410f13151271e8006
  React-jsinspector: b511447170f561157547bc0bef3f169663860be7
  React-logger: c5b527272d5f22eaa09bb3c3a690fee8f237ae95
  react-native-add-calendar-event: b9802a8be8ad4beb0dcb3efb28969c077558fb29
  react-native-blur: db09583409bd872539bf6e915df484d792373810
  react-native-camera: 3eae183c1d111103963f3dd913b65d01aef8110f
  react-native-config: 3367df9c1f25bb96197007ec531c7087ed4554c3
  react-native-context-menu-view: f20df4bf07145c6d14ef631c8b8663ccdac9fb7c
  react-native-date-picker: 312c387d2ff873c66c5e4cf78ff6827fa91644e2
  react-native-device-country: 19c64cbe9ba116b54fccc9ddeaade5d1923e8317
  react-native-geolocation-service: 608e1da71a1ac31b4de64d9ef2815f697978c55b
  react-native-get-random-values: 21325b2244dfa6b58878f51f9aa42821e7ba3d06
  react-native-google-maps: b80d3947411fcf462ec420c3e6122b7fa24beb9a
  react-native-keyboard-controller: 8f9e5b1cbab066f3269bcd7f12601ee2a85c9b98
  react-native-maps: 79610ffcd0ec2ca8e0cefb48aa8ae7c62a75d7cc
  react-native-netinfo: 48c5f79a84fbc3ba1d28a8b0d04adeda72885fa8
  react-native-onesignal: 8d8f7d5e2cc44239fc0ec72fe9c6d27b939870fc
  react-native-pager-view: e8199494ce34493760c0040fd3b1017a630e3b81
  react-native-safe-area-context: 141eca0fd4e4191288dfc8b96a7c7e1c2983447a
  react-native-slider: e850b244209a8a04ac4b889abfcff911f568d061
  react-native-splash-screen: 4312f786b13a81b5169ef346d76d33bc0c6dc457
  react-native-version-check: 54a471d70dadf0f72fc142e70f16e4bf473a91a5
  react-native-wallet: 943e7354ed0b370243a59e74e6d14e2e9dd6a1ca
  react-native-webview: 55b08a0f432abd52ab51fbb7ec6e0fd60c56aedc
  React-NativeModulesApple: 0c52daa4b48fbb67ea64d498f1f534dff50958c1
  React-perflogger: 6bd153e776e6beed54c56b0847e1220a3ff92ba5
  React-RCTActionSheet: c0b62af44e610e69d9a2049a682f5dba4e9dff17
  React-RCTAnimation: fe7005136b58f58871cab2f70732343b6e330d30
  React-RCTAppDelegate: 004661e37e38e5378b42e7b40b3843e2ed9adf43
  React-RCTBlob: 7c1544c4581313d7f07dc2130bb1622cebde8039
  React-RCTImage: f80d68a674b84db1322bbe287d82501fcd7b28b1
  React-RCTLinking: a3cf63eb18070cfa90499ee9cbbc88ad33338b3d
  React-RCTNetwork: abde2f1c54ad9b42824685a8b2c854a25274d719
  React-RCTSettings: 6cb55e98d630e3594482b5a790c7655eddd805bd
  React-RCTText: ****************************************
  React-RCTVibration: ea3a68a49873a54ced927c90923fc6932baf344a
  React-rncore: 9672a017af4a7da7495d911f0b690cbcae9dd18d
  React-runtimeexecutor: 369ae9bb3f83b65201c0c8f7d50b72280b5a1dbc
  React-runtimescheduler: 8c61cfbb28ec48d9389de68016603150c7ed1d27
  React-utils: 439e449a149ae990207fa04f7a645898b6d4a195
  ReactCommon: 51c8801bc6f43f442c6b31280034b3e1219d2466
  RNAppleAuthentication: b2d8b5ccba86b5b1e55a1a2e858f7ac6d8a21816
  RNCalendarEvents: 7e65eb4a94f53c1744d1e275f7fafcfaa619f7a3
  RNCAsyncStorage: ec53e44dc3e75b44aa2a9f37618a49c3bc080a7a
  RNCCheckbox: 471ebdc33884c084f362615c1ef3507496cc07f1
  RNCMaskedView: 949696f25ec596bfc697fc88e6f95cf0c79669b6
  RNDeviceInfo: 59344c19152c4b2b32283005f9737c5c64b42fba
  RNFastImage: 5c9c9fed9c076e521b3f509fe79e790418a544e8
  RNFBAnalytics: 9d6f836ddcd22ab52cac0f79440dd0027b47b2d2
  RNFBApp: 2d39ec9a9dd25f18e96009053af087b9ed714626
  RNFBAuth: 44bc835c2a5a62fecf71c498e75a7b2c065eed35
  RNFBCrashlytics: 747d93061b832d447e2f18186b90e69b9007c173
  RNFBDynamicLinks: ccade1696ff0b4b1dfc2e316e773df06375ce3b8
  RNFBFirestore: 0e9dab5312424d0b5cc98633da8bcbfa47ff90f2
  RNFBMessaging: ed43c957c714ca8baa6c0f09704fabaa9601c48a
  RNFBPerf: 032e2451f91e87e14654073b66efa12e14ca67a9
  RNFBStorage: 98786e2b6d5832c6d8062ff74c8ec9c41605ac0a
  RNFlashList: 790975a3c1bdd4c87c908d8f4a60f04f27af9eb0
  RNGestureHandler: c0d04458598fcb26052494ae23dda8f8f5162b13
  RNGoogleSignin: 9d0ecc3bff939ccef83cb3a26b874130b06a6e81
  RNImageCropPicker: 771e2ca319d2cf92e04ebf334ece892ee9a6728f
  RNNotifee: 935f3ea8c134c88cbf8b13ea0c97c72c09ad2116
  RNPermissions: 2df889db6e3b66dfca54be7e43b8e35437b98b35
  RNReactNativeHapticFeedback: 800e407fdbac73b9e6e00b6cc256a364de875d24
  RNReanimated: 726395a2fa2f04cea340274ba57a4e659bc0d9c1
  RNScreens: d6da2b9e29cf523832c2542f47bf1287318b1868
  RNShare: 9e813d9abf8d60dd01d190eca6bc57fbbd1a7144
  RNSVG: af3907ac5d4fa26a862b75a16d8f15bc74f2ceda
  RNVectorIcons: b0f3ecdf598c397ae0c23e688a121b6eeadcf286
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Stripe: d546a79759fb2b0983076d2551096b91d032693a
  stripe-react-native: 2dc337add84f472f055ec87b138b04fe915d17e3
  StripeApplePay: fd4aeaa8af1ebbfe5e38390ef7b4607f66494f80
  StripeCore: 4999b0c234127b28b9e656caa558ba4406ce58b3
  StripeFinancialConnections: 4e50d2395e74c7637f84fc9d89fa5740b8240f49
  StripePayments: a1260c9fecdb8640462c1fe70c4e2e17df2ab3a8
  StripePaymentSheet: 155d402ee01447002d23c982e905ce06e6d7ef66
  StripePaymentsUI: 2084d4398cb4869b13dd9eac43a00481476b152f
  StripeUICore: 2c115cc8498f3badc19b4a41b3b1fdac0ae21d41
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  Yoga: 8796b55dba14d7004f980b54bcc9833ee45b28ce

PODFILE CHECKSUM: a28a48583761b11717b23a85e758bd0416d01ff6

COCOAPODS: 1.16.2
