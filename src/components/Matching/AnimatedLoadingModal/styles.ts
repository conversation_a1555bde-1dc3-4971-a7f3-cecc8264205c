import {StyleSheet} from 'react-native';
import {lightTheme, darkTheme} from '~constants/colors';

const createStyles = (colors: typeof lightTheme | typeof darkTheme) => {
  return StyleSheet.create({
    buttonWrapper: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      paddingHorizontal: 20,
    },
    loaderView: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    absolute: {position: 'absolute'},
    container: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    headerText: {
      fontSize: 24,
      fontWeight: '600',
      color: colors.white,
      textAlign: 'center',
      marginBottom: 40,
      letterSpacing: 0.5,
      textShadowColor: colors.black + '30',
      textShadowOffset: {width: 0, height: 2},
      textShadowRadius: 4,
    },
    circleContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
    },
    qrCodeText: {
      zIndex: 100,
      marginTop: 10,
      fontWeight: 'bold',
      fontSize: 14,
      color: colors.white,
      width: 250,
      lineHeight: 22,
      textAlign: 'center',
    },
    progressText: {
      zIndex: 100,
      fontSize: 80,
      color: colors.white,
      width: 300,
      height: 202,
      textAlign: 'center',
    },
    button: {
      position: 'absolute',
      bottom: 80,
      width: '70%',
      height: 60,
      backgroundColor: colors.white + '26',
      borderWidth: 1,
      borderColor: colors.white + '4D',
      borderRadius: 30,
      alignSelf: 'center',
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.3,
      shadowRadius: 4.65,
      elevation: 8,
    },
    buttonText: {
      fontSize: 18,
      color: colors.white,
      fontWeight: '600',
      letterSpacing: 1,
      marginRight: 10,
    },
    closeIcon: {
      width: 24,
      height: 24,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.white + '33',
      borderRadius: 12,
      marginLeft: 8,
    },
    closeIconText: {
      color: colors.white,
      fontSize: 16,
      fontWeight: '600',
    },
    linearGradientContainer: {position: 'absolute', width: '100%', height: '100%'},
  });
};

export const styles = createStyles;
