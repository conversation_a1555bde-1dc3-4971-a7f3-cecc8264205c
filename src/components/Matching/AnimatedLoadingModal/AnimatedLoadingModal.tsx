import {useCallback, useEffect} from 'react';
import {Text, TouchableOpacity, View} from 'react-native';
import Animated, {
  FadeInUp,
  FadeOutDown,
  runOnJS,
  useAnimatedProps,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {useSafeAreaFrame} from 'react-native-safe-area-context';
import Svg, {Circle} from 'react-native-svg';
import {CustomModal} from '~components/CustomModal';
import {ModalHeight} from '~types/modal';
import {styles as createStyles} from './styles';
import {ReText} from 'react-native-redash';
import LinearGradient from 'react-native-linear-gradient';
import {useTranslation} from 'react-i18next';
import {useTheme} from '~contexts/ThemeContext';
import { ImageBackground } from 'react-native';

interface IProps {
  isVisible: boolean;
  callback: () => void;
  isButtonVisible: boolean;
  close: () => void;
  isQRCode?: boolean;
}

const CIRCLE_LENGTH = 1000;
const R = CIRCLE_LENGTH / (2 * Math.PI);

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const AnimatedLoadingModal = ({isVisible, callback, isButtonVisible, close, isQRCode}: IProps) => {
  const {colors} = useTheme();
  const styles = createStyles(colors);
  const progress = useSharedValue(0);
  const {t} = useTranslation();

  const {width, height} = useSafeAreaFrame();

  const animatedProps = useAnimatedProps(() => ({
    strokeDashoffset: CIRCLE_LENGTH * (1 - progress.value),
  }));

  const progressText = useDerivedValue(() => {
    return `${Math.floor(progress.value * 100)} %`;
  });

  const triggerCallback = useCallback(() => {
    progress.value = withTiming(progress.value > 0 ? 0 : 1, {duration: 2000}, finished => {
      if (finished) {
        runOnJS(callback)();
      } else {
        runOnJS(close)();
      }
    });
  }, [callback, close, progress]);

  useEffect(() => {
    if (isVisible) {
      setTimeout(() => {
        triggerCallback();
      }, 300);
    } else {
      setTimeout(() => {
        progress.value = 0;
      }, 400);
    }
  }, [isVisible]);
  return (
    <CustomModal modalIsVisible={isVisible} onCloseModal={() => {}} height={ModalHeight.MAX}>
      <ImageBackground
        defaultSource={require('~assets/images/matching_bg.png')}
        style={styles.linearGradientContainer}
        resizeMode={'stretch'}
        source={require('~assets/images/matching_bg.png')}>
        <View style={styles.container}>
          <ReText style={styles.progressText} text={progressText} />
          {isQRCode && <Text style={styles.qrCodeText}>One moment, we're generating your QR code</Text>}
          <Svg style={styles.absolute} width={width} height={height} viewBox={`0 0 ${width} ${height}`}>
            <Circle cx={202} cy={height / 2} r={R} stroke={colors.secondary + '4D'} strokeWidth={30} fill="none" />
            <AnimatedCircle
              cx={width / 2}
              cy={height / 2}
              r={R}
              fill="none"
              stroke={colors.primary}
              strokeWidth={15}
              strokeDasharray={CIRCLE_LENGTH}
              animatedProps={animatedProps}
              strokeLinecap={'round'}
            />
          </Svg>
        </View>
        {isButtonVisible && (
          <Animated.View style={styles.button} entering={FadeInUp.duration(200)} exiting={FadeOutDown.duration(200)}>
            <TouchableOpacity onPress={close} style={styles.buttonWrapper}>
              <Text style={styles.buttonText}>{t('events.no_matches')}</Text>
              <View style={styles.closeIcon}>
                <Text style={styles.closeIconText}>×</Text>
              </View>
            </TouchableOpacity>
          </Animated.View>
        )}
      </ImageBackground>
    </CustomModal>
  );
};

export default AnimatedLoadingModal;
